import { type IFpVideoData } from '../modules/fp-video-service/types';
import { type IBannerbotBusinessDetails } from '../types/bannerbot';
import { AdLanguage } from '../types/campaign_details';

export const getKeyBenefitsPrompt = (
  params: Partial<IBannerbotBusinessDetails>,
): string => {
  const { product_or_service_description: productOrServiceDescription } =
    params;

  return `
You are an AI assistant. 
Based on the product or service description provided below, generate a JSON object with key benefits of the product or service. 
Make sure to include 2-3 benefits, keeping them short and impactful.

Product or Service Description:
"${productOrServiceDescription}"

Expected Output:
{
  "key_benefits": [
    "Benefit 1",
    "Benefit 2",
    "Benefit 3"
  ]
}
  `;
};

export const getAdBannersPrompt = (
  params: Partial<IBannerbotBusinessDetails>,
): string => {
  const {
    product_or_service_description: productOrServiceDescription,
    key_benefits: keyBenefits,
  } = params;

  return `
You are an intelligent AI assistant dedicated to generating ad creative banners.

A banner consists of four components:

1. creative_title - An attention-grabbing headline that clearly communicates the message. It should be short, informative, and intriguing. Examples: "Delicious Biryanis," "Biryani Hours."
2. call_out - A subtitle to the creative_title that highlights reasons why someone should make a purchase. It should provide concise product details as well as contact or website details. Examples: "Up to 50% Off," "Limited Stocks," "With Extra Chicken.", "Quick and Easy"
3. call_to_action - An element that encourages users to take action. Examples: "Order Now," "Click Here," "Hurry." It should not exceed 15 characters.
4. creative_image - An image that helps users visualize the offerings. Examples: "Men eating biryani," "Biryani in a bowl," "Chef cooking biryani."

Now consider the following product (or service) details provided by the user:

Product or Service Description:
"${productOrServiceDescription}"

Key Benefits:
"${keyBenefits?.join(', ')}"

### Important Instruction for creative_image_keywords:

- The **creative_image_keywords** are used to fetch relevant images from stock libraries.
- Keywords should be chosen to maximize the chances of finding high-quality stock images.
- Avoid technical or UI-related keywords that are unlikely to have stock images.
- Use **generic yet relevant** keywords that represent real-world concepts and visuals.

Based on the above product details and your training, please return an array of 5 banners, in the following JSON format:

{
  banners: [
    {
      "creative_title": "",
      "call_out": "",
      "call_to_action": "",
      "creative_image_keywords": []
    }
  ]
}

For creative_title, call_out, and call_to_action, use the same language as mentioned in product details.
For **creative_image_keywords**, always provide **relevant but generic** keywords in English, ensuring they are likely to return quality stock images.

Please provide a valid JSON response without any special characters. Remember, your response should be JSON-parsable.
`;
};

const getBusinessDetailsString = (
  businessDetails: Partial<IBannerbotBusinessDetails>,
): string => {
  return `
  - **Product/Service**: ${
    businessDetails.product_or_service_description ?? 'Not specified'
  }
  - **Website**: ${businessDetails.website ?? 'Not provided'}
  - **Contact**: ${businessDetails.mobile ?? 'Not provided'}
  - **Business Name**: ${businessDetails.business_name ?? 'Not specified'}
  - **Key Benefits**: ${
    businessDetails.key_benefits?.join(', ') ?? 'Not specified'
  }
  `;
};

export const getVideoDataPrompt = (params: {
  businessDetails: Partial<IBannerbotBusinessDetails>;
  sampleVideoData: IFpVideoData;
  language?: AdLanguage;
}): string => {
  const { businessDetails, sampleVideoData, language } = params;
  const resolvedLanguage = language ?? AdLanguage.ENGLISH;

  return `
  Given the following sample video data, update it to match the provided business details. Ensure that branding, text, and key messages are aligned with the business's product or service.
  Use ${resolvedLanguage} as preferred content language

  ## Business Details:
  ${getBusinessDetailsString(businessDetails)}
  - **Logo**: ${
    businessDetails.business_logo?.square?.url ?? 'No logo provided'
  }

  ## Sample Video Data:
  ${JSON.stringify(sampleVideoData, null, 2)}

  ### Instructions:
  1. **Strictly Maintain Node Structure**:
     - Do **not add, remove, or modify any nodes** in the JSON structure.
     - Only update the **text content** and **branding elements** (e.g., business name, logo, website).

  2. **Text Replacement**:
     - Replace all text fields (e.g., titles, descriptions, captions) with relevant business details.
     - Use the provided **Product/Service**, **Key Benefits**, and other business details to update the text.
     - If any scene's text includes contact information such as a website or phone number, ensure it is replaced with the correct values provided in business details. If the details are missing, update the text appropriately without inventing any information.
     - Never add made-up contact details like a website if they are not present in the business information

  3. **Branding Updates**:
     - Replace branding elements (e.g., business name, logo, website) with the provided business details.
     - If no logo is provided, keep the url as empty.
     - If no website is provided, keep the website as empty.
     - If the business name is not provided, keep it empty, don't assume the business name and don't use any placeholder text.

  4. **Media Assets**:
     - Do **not modify** any media assets (e.g., audio, video, images) except for branding elements (e.g., logo).

  5. **Consistency**:
     - Ensure the updated text and branding are consistent with the business category and key messages.

  ### Output:
  Return the updated JSON structure with the **exact same node structure** as the sample video data, but with updated text and branding.
  `;
};

export const getImageAndVideoKeywordsPrompt = (params: {
  businessDetails: Partial<IBannerbotBusinessDetails>;
}): string => {
  const { businessDetails } = params;

  return `
  For the following business, generate relevant image and video keywords.

  ## Business Details:
  ${getBusinessDetailsString(businessDetails)}
  - **Logo**: ${
    businessDetails.business_logo?.square?.url ?? 'No logo provided'
  }

  ## Instructions:
  1. **Image Keywords:** 
      - Generate 5-10 image keywords that visually represent the business, product, or service.
      - Keywords should describe relevant objects, people, scenes, emotions, and activities.
      - Use English language only.
  2. **Video Content Description:** 
      - Create a **generic yet relevant video content description** involving people. 
      - This description will be used to fetch relevant stock videos.
      - It should depict relatable scenarios involving people interacting with the business, product, or service.
      - Use clear and visual-friendly descriptions (5-10 words).
      - Always in English.

  ## Examples of valid **video_content_description_involving_people**:
  - "Group of men discussing in meeting room"
  - "Young couple opening the door to their new home"
  - "Friends chatting in a well-lit living space"
  - "Happy customers enjoying a meal at a restaurant"
  - "Team collaborating on a project in an office"

  ## Output Format:
  Return the data in the following JSON format:

  {
    "image_keywords": [
      "keyword1", 
      "keyword2", 
      "keyword3",
      "..."
    ],
    "video_content_description_involving_people": "Concise and generic stock video description (5-10 words)"
  }
  `;
};

export const getImageAndVideoKeywordsPromptV2 = (params: {
  businessDetails: Partial<IBannerbotBusinessDetails>;
}): string => {
  const { businessDetails } = params;

  return `
  You are a creative visual strategist helping generate powerful keywords to find high-quality **stock images and videos** for advertising a business.

  Your task is to analyze the provided business details and generate:

  ---

  ### 🎯 Output:

  1. **image_keywords**  
    A list of 5–10 **concise and relevant image search keywords**.  
    These should be highly visual, emotionally engaging, and specific enough to return meaningful stock images.

  2. **video_content_description_involving_people**  
    A short (5–10 word) **visually clear video scene** that involves **people interacting** with the business, product, or service in a realistic setting.

  ---

  ### ✅ Guidelines for Effective Keywords

  #### 📸 For image_keywords:
  - Each keyword should describe a distinct **visual concept** — an object, scene, activity, or emotion related to the business.
  - Prefer phrases over single words.
  - Think like a photographer: what would a good image for this business show?
  - Include visual descriptors: e.g., “minimalist coffee cup on desk” instead of just “coffee”.

  ✅ Good examples:
  - “barista pouring latte art in cafe”
  - “flat lay of skincare products on towel”
  - “happy customers leaving a small shop”
  - “young woman using a fitness tracker”

  🛑 Avoid:
  - Brand names, logo text, price info
  - Abstract or overly generic terms like “success”, “growth”, “awesome”, etc.

  ---

  #### 🎥 For video_content_description_involving_people:
  - Describe a realistic short scene involving **people** engaging with the type of product/service.
  - Think like a filmmaker: describe what’s happening **visually**.
  - Keep it **general enough** to work across many stock platforms, but specific enough to paint a mental image.

  ✅ Good examples:
  - “Group of friends painting shoes together”
  - “Woman paying for coffee at local cafe”
  - “Children playing in a bright classroom”
  - “Man browsing items in a retail store”
  - “Team celebrating success in office”

  🛑 Avoid:
  - Mentioning the business name
  - Pricing or discount language
  - UI or app features that can't be filmed directly

  ---

  ## Business Details:
  ${getBusinessDetailsString(businessDetails)}
  - **Logo**: ${
    businessDetails.business_logo?.square?.url ?? 'No logo provided'
  }
  
  ---

  ### ✅ Output Format:
  Return only the following JSON:

  \`\`\`json
  {
    "image_keywords": [
      "keyword1",
      "keyword2",
      "keyword3",
      "..."
    ],
    "video_content_description_involving_people": "Short scene description here"
  }
  \`\`\`
  `;
};

export const getAiBannerImageGenPrompt = (params: {
  business_details: Partial<IBannerbotBusinessDetails>;
  banner_details: {
    creative_title: string;
    call_out: string;
    call_to_action: string;
    focused_usp: string;
    imagery: string;
  };
}): string => {
  const { business_details: businessDetails, banner_details: bannerDetails } =
    params;

  return `
Generate a mobile-optimized ad banner visual prompt for Ideogram AI using these requirements:

### Core Elements (INCLUDE ALL):
{
  "components": {
    "usp": "${bannerDetails.focused_usp}",
    "title": "${bannerDetails.creative_title}",
    "call_out": "${bannerDetails.call_out}", 
    "cta": "${bannerDetails.call_to_action}",
    "base_imagery": "${bannerDetails.imagery}"
  },
  "business_context": ${getBusinessDetailsString(businessDetails)},
}

### Visual Requirements:
1. **People**:
   - Natural expressions matching message tone

2. **Text Implementation**:
   - Title: Top 20% area, bold sans-serif, high contrast outline
   - Call-out: Left/right third, semi-transparent background
   - CTA: Fixed bottom-center pill-shaped button

3. **Style & Composition**:
   - Depth layers: Foreground product, mid-ground people, background context

4. **Ideogram Optimization**:
   - Avoid text effects that require complex rendering
   - Use realistic lighting/shadow patterns
   - Include negative space for platform UI overlays

### Output Format (STRICT JSON):
{
  "prompt": "[Scene details] Title: '[text]' [position/style], Call-out: '[text]' [position/style], CTA: '[text]' [style]. Style: [genre], Cultural Elements: [specifics], Color Palette: [primary/secondary]"
}

Return only valid JSON. No commentary.
`.trim();
};

export const getUspAndBannerElementDetailsPrompt = (params: {
  business_details: Partial<IBannerbotBusinessDetails>;
}): string => {
  const { business_details: businessDetails } = params;

  return `
You are a **performance-focused creative designer** tasked with generating Meta Ads banners. Follow these rules:

1. Create 5 unique banners in this JSON format:
\`\`\`json
{
  "banners": [
    {
      "creative_title": "(max 8 words, focus on benefit/problem solved)",
      "call_out": "(specific reason to believe - use product features/benefits)",
      "call_to_action": "<15 chars, action-driven: 'Shop Now', 'Get Offer'", 
      "creative_image_keywords": ["3-5 EN stock phrases", "related to product/benefit"],
      "imagery": "(visual scene details + branding elements from website if provided)",
      "focused_usp": "(unique selling proposition from key benefits or generated)"
    }
  ]
}
\`\`\`

2. **Content Strategy**:
- Use these business details: 
  ${getBusinessDetailsString(businessDetails)}
- If Key Benefits exist: Make each banner focus on ONE different benefit as its USP
- No Key Benefits? Invent unique USPs that match product description
- Website domain hints? Incorporate relevant style/imagery elements

3. **Diversity Requirements**:
- Vary angles: 2 product-focused, 2 problem/solution, 1 social proof
- Never repeat CTAs or USPs
- Image keywords must differ significantly between banners

4. **Conversion Best Practices**:
- Use urgency language in 2 banners ("Limited Time", "Exclusive")
- Include price savings mention if implied in product description
- Add subtle website branding in 3 banners' imagery descriptions

Return ONLY valid JSON. No markdown or explanations.
`.trim();
};

// TODO: clean
// export const getVoiceOverScriptPrompt = (params: {
//   business_details: Partial<IBannerbotBusinessDetails>;
//   language?: AdLanguage;
// }): string => {
//   const toneInstruction =
//     params.language === AdLanguage.HINDI
//       ? 'Use a conversational, energetic Hindi tone. Think influencer-style—casual, punchy, and relatable. Keep sentences short and easy to speak.'
//       : 'Use a modern, conversational English tone. Think TikTok/YouTube Shorts style—engaging, clear, and naturally flowing.';

//   return `
//   You are a voice-over scriptwriter for 15-20 second social media video ads.

//   Business/Product Details:
//   "${getBusinessDetailsString(params.business_details)}"

//   CORE REQUIREMENTS:
//   - Write 1 voice-over script for 15-20 seconds
//   - Focus on the SPECIFIC product/service from the business details above
//   - Use the actual business name, product features, and unique selling points
//   - Avoid generic phrases - make it about THIS specific business
//   - Language: ${
//     params.language === AdLanguage.HINDI ? 'Hindi (Devanagari)' : 'English'
//   }
//   - ${toneInstruction}
//   - Write ALL numbers in English numerals (1, 2, 3, not देवनागरी)
//   - For currency: Use "Rs 16999" for INR, "100 $" for USD (number + currency symbol)

//   RESTRICTIONS:
//   - NO contact info (phone, email, website, social handles)
//   - NO emojis, symbols, or special characters

//   SCRIPT STRUCTURE:
//   - **Hook (0-3 sec)**: Problem/pain point your specific product solves
//   - **Solution (3-12 sec)**: How THIS business/product specifically helps
//   - **Action (12-20 sec)**: Clear next step ("Get yours today", "Visit us now")

//   VOICE-OVER OPTIMIZATION:
//   - Short, punchy sentences
//   - Easy to pronounce and deliver with energy
//   - Natural speech flow with strategic pauses (...)
//   - Mention specific product names, features, or benefits from the business details

//   Return as JSON:
//   {
//     "script": "Your product-specific voice-over script here"
//   }
// `;
// };

// TODO: clean
// export const getVoiceOverScriptV2Prompt = (params: {
//   business_details: Partial<IBannerbotBusinessDetails>;
//   language?: AdLanguage;
// }): string => {
//   const toneInstruction =
//     params.language === AdLanguage.HINDI
//       ? 'Use a conversational, energetic Hindi tone. Think influencer-style—casual, punchy, and relatable. Keep sentences short and easy to speak.'
//       : 'Use a modern, conversational English tone. Think TikTok/YouTube Shorts style—engaging, clear, and naturally flowing.';

//   return `
//   You are a voice-over scriptwriter for 15-20 second social media video ads.

//   Business/Product Details:
//   "${getBusinessDetailsString(params.business_details)}"

//   CORE REQUIREMENTS:
//   - Write 1 voice-over script for 15-20 seconds
//   - Focus on the SPECIFIC product/service from the business details above
//   - Use the actual business name, product features, and unique selling points
//   - Avoid generic phrases - make it about THIS specific business
//   - Language: ${
//     params.language === AdLanguage.HINDI ? 'Hindi (Devanagari)' : 'English'
//   }
//   - For contact information, naturally direct viewers to read contact details from the screen instead of mentioning specific numbers or addresses
//   - ${toneInstruction}
//   - Write ALL numbers as spoken words for easy voice-over reading:
//     * English: 3 → "three", 300 → "three hundred", 16999 → "sixteen thousand nine hundred ninety nine"
//     * Hindi: 3 → "तीन", 300 → "तीन सौ", 16999 → "सोलह हजार नौ सौ निन्यानवे"
//   - For currency symbols:
//     * English: $ → "dollar", Rs → "rupees"
//     * Hindi: $ → "डॉलर", Rs → "रुपये"
//   - **IMPORTANT**: Wrap ALL numbers, currency amounts in curly braces with visual format in square brackets
//     * Format: {spoken_text}[visual_format]
//     * Example: "Get it for {sixteen thousand nine hundred ninety nine rupees}[Rs 16999]"
//     * Example: "सिर्फ {तीन हजार रुपये}[Rs 3000] में"
//     * Example: "{तीन सौ}[300]"

//   RESTRICTIONS:
//   - NO emojis, symbols, or special characters
//   - NO generic business advice
//   - Don't use any examples specifed in the examples. Get the actual values from the business details or don't show them.

//   VOICE-OVER OPTIMIZATION:
//   - Short, punchy sentences
//   - Easy to pronounce and deliver with energy
//   - Natural speech flow with strategic pauses \`...\`
//   - Mention specific product names, features, or benefits from the business details

//   Return as JSON:
//   {
//     "script": "Your product-specific voice-over script with {spoken}[visual] format"
//   }
// `;
// };

export const getVoiceOverScriptV3Prompt = (params: {
  business_details: Partial<IBannerbotBusinessDetails>;
  language?: AdLanguage;
}): string => {
  const toneInstruction =
    params.language === AdLanguage.HINDI
      ? 'Use a conversational, energetic Hindi tone. Keep it conversational, influencer-style, casual and punchy. Prefer common English words like “flexibility” instead of complex Hindi like “लचीलापन”. Keep sentences short and easy to speak.'
      : 'Use a modern, conversational English tone. Think TikTok/YouTube Shorts style—engaging, clear, and naturally flowing.';

  return `
  You are a voice-over script writer for ~15 seconds social media video ads.

  Business/Product Details:
  "${getBusinessDetailsString(params.business_details)}"

  CORE REQUIREMENTS:
  - Write 1 voice-over script for ~15 seconds
  - Focus on the SPECIFIC product/service from the business details above
  - Use the actual business name, product features, and unique selling points
  - Avoid generic phrases - make it about THIS specific business
  - Try to come up with script that focuses on most of the products the user is trying to sell.
  - create a sense of urgency in the script.
  - Language: ${
    params.language === AdLanguage.HINDI ? 'Hindi (Devanagari)' : 'English'
  }
  - ${toneInstruction}
  - when ever you stumble upon numbers or symbols or phrases that have a differnet way of written form and spoken form, then write them in the **format: {spoken_text}[visual_format]**. some examples can be:
    * "Get it for {sixteen thousand nine hundred ninety nine rupees}[Rs 16999]" 
    * "सिर्फ {तीन हजार रुपये}[Rs 3000] में"
    * "{तीन सौ}[300]"
    * "{ten percent}[10%]"
    * "{दस बजे सुबह}[10 AM]"
    * "{Doctor}[Dr.]"
    * "{रुपये}[₹]"
    * "book {three nights, four days}[3N/4D] tour"
    make sure that the spoken_text follows the language specified and the visual_format conveys the exact meaning of the spoken_text

  Contact Information:
  - For contact information, naturally direct viewers to read contact details from the screen instead of mentioning specific numbers or addresses.
  - don't mention to visit website if the product doesn't contain one.

  Structure the script as:
  1. **Hook** – Start with a relatable situation or emotional trigger  
  2. **Transformation** – Show how the product/service solves that pain or creates joy  
  3. **Offer or Highlight** – Mention the deal, key feature, or unique benefit  
  4. **Call to Action** – End with urgency and clear next step (call now, book today, etc.)

  RESTRICTIONS:
  - NO emojis, symbols, or special characters  
  - NO generic business advice
  - Don't use any examples specifed in the examples. Get the actual values from the business details or don't show them.

  VOICE-OVER OPTIMIZATION:
  - Shorter lines for easier voice-over and better retention.
  - Easy to pronounce and deliver with energy
  - keep the pace designed for Reels/ Youtube
  - Mention specific product names, features, or benefits from the business details

  HIGHLIGHTS:
  - mention 3 major highlits of the product in short which the user will be interested in looking.
  - keep each highlight short, each with less than 6 words.
  - don't give any generic highlights. promote the product only.
  - write the numbers, prices and symbols as their visual form in highlights no need of spoken form here.
  - it should always be in English

  Return as JSON:
  {
    "script": "product-specific voice-over script with {spoken_text}[visual_text] format",
    "highlights": [
        "array of string which highlits the product"
    ]
  }
`;
};

export const getVideoAdDirectorPromptV3 = (params: {
  business_details: Partial<IBannerbotBusinessDetails>;
  script: string;
}): string => {
  return `
  You are a creative video ad director specializing in sourcing visually engaging and product-relevant stock footage from platforms like Pexels and Pixabay.

  Your task is to analyze a video voiceover script and break it into distinct visual chunks. For each chunk, generate the **best possible search keyword** — a phrase someone could use to find relevant stock video content that brings the script to life visually.

  ---

  🎯 OBJECTIVE

  Generate **cinematically descriptive**, emotionally resonant, and **stock platform-friendly** keyword phrases that can return usable results on free stock video platforms like Pexels or Pixabay.

  ---

  📌 GUIDELINES FOR WRITING GOOD SEARCH KEYWORDS

  1. **Use Full Phrases, Not Single Words**  
    Avoid vague or overly broad terms. Be specific about who is doing what, where, and how.  
    Example:  
    - ❌ "shoes"  
    - ✅ "woman lacing colorful sneakers on street corner"

  2. **Include Visual & Cinematic Descriptors**  
    Add modifiers like:  
    - "close up", "aerial view", "slow motion", "timelapse", "4k", "b-roll", etc.

  3. **Match Action + Emotion**  
    Capture what’s happening and how the subject feels.  
    Example:  
    - “excited children painting at art class”  
    - “relaxed man sipping coffee on balcony at sunrise”

  4. **Think Like a Filmmaker, Not a Copywriter**  
    Imagine how each line of the script would look on screen. Convert abstract lines (like pricing, features, or calls to action) into visual representations:
    - For pricing → show people interacting with the product or booking
    - For urgency → show "finger tapping phone", "booking page on screen", etc.

  5. **Optimize for Stock Discoverability**  
    Use terminology commonly found in stock metadata (e.g., “group of friends”, “woman smiling”, “creative workshop”, etc.) rather than niche brand or product terms.

  ---

  🧾 OUTPUT FORMAT

  Output a JSON object with the following structure:

  \`\`\`json
  {
    "chunks": [
      {
        "script": "Exact text from this segment",
        "keyword": "Search keyword phrase to find matching footage"
      },
      ...
    ]
  }
  \`\`\`

  ## Input Details
  ### Product Context
  ${getBusinessDetailsString(params.business_details)}

  ### Input Script
  ${params.script}
  `;
};

export const getVideoAdDirectorPromptV4 = (params: {
  business_details: Partial<IBannerbotBusinessDetails>;
  script: string;
}): string => {
  return `
  You are a creative advertising strategist and AI assistant helping generate **high-conversion keywords** for stock videos used in short video ads on platforms like **Meta, TikTok, and Instagram Reels**.

  ## 🎯 Goal
  You will be given:
  - A full ad **video script**
  - A brief **product description**

  Your task is to:
  1. **Break the script into meaningful chunks** (usually each line or distinct visual idea)
  2. For each chunk, generate **one relevant stock video keyword or phrase** that:
    - Reflects the **core message** of the product, with focus on the chunk
    - Describes a **real-life action or scene** likely to be available on stock platforms
    - Is **optimized for short-form video ads** and high visual engagement

  ## 📱 Important Visual Guidelines
  - Prioritize **mobile-friendly, portrait-oriented scenes**
  - Prefer **close-up shots**, **people-centric actions**, and **natural environments**
  - Use keywords that are **b-roll friendly**, **cinematic**, and easy to match with voiceover timing

  ## 🔍 Stock Keyword Best Practices
  - Be **descriptive, not literal** (don’t use product/brand names)
  - Focus on **people doing relatable things** (not just objects)
  - Include **context + action + emotion + setting**, e.g.:
    - “woman smiling while scrolling phone on couch”
    - “man using skincare in morning light”
    - “family unboxing package with excitement”

  ## 🧪 Output Format (JSON)
  \`\`\`json
  {
    "chunks": [
      {
        "script": "<chunk of the script>",
        "keyword": "<best stock search keyword or phrase>"
      },
      ...
    ]
  }
  \`\`\`

  ---

  ## CONTEXT FOR THIS PROJECT

  ### Product/Business Details:
  ${getBusinessDetailsString(params.business_details)}

  ### Script to Analyze:
  ${params.script}
`;
};

/**
 * tried sth new instead of keyword explaning the chunk
 * the keywords themselves try to sell the product
 */
export const getVideoAdDirectorPromptV5 = (params: {
  business_details: Partial<IBannerbotBusinessDetails>;
  script: string;
}): string => {
  return `
  You are a creative advertising strategist and AI assistant helping generate **high-conversion keywords** for stock videos used in short video ads on platforms like **Meta, TikTok, and Instagram Reels**.

  ## 🎯 Goal
  You will be given:
  - A full ad **video script**
  - A brief **product description**

  Your task is to:
  1. **Break the script into meaningful chunks** (usually each line or distinct visual idea)
  2. For each chunk, generate **one relevant stock video keyword or phrase** that:
    - **Maintains product context throughout** - every keyword should connect to the specific product/service
    - Reflects the **core message** of the product, with focus on the chunk
    - Describes a **real-life action or scene** likely to be available on stock platforms
    - Is **optimized for short-form video ads** and high visual engagement

  ## 🧬 Product Context Rules (CRITICAL)
  
  **NEVER generate generic keywords that could apply to any business**
  
  Every keyword MUST include elements that connect to your specific product:
  - **Industry context**: sales, fitness, food, finance, etc.
  - **Technology/tool context**: WhatsApp, CRM, AI, app, dashboard, etc.
  - **User/customer context**: sales team, entrepreneurs, families, etc.
  - **Action context**: messaging, lead management, ordering, tracking, etc.

  ### ❌ GENERIC (AVOID):
  - "happy people using technology" → Could be anything
  - "person on phone" → Too vague
  - "team meeting discussion" → No product connection
  - "data analysis sorting" → Generic business

  ### ✅ PRODUCT-SPECIFIC (GOOD):
  - "sales team checking WhatsApp leads" → WhatsApp + sales context
  - "entrepreneur reviewing CRM dashboard" → CRM + business context  
  - "fitness trainer using workout app" → Fitness + app context
  - "family ordering food delivery phone" → Food delivery context

  ## 📱 Important Visual Guidelines
  - Prioritize **mobile-friendly, portrait-oriented scenes**
  - Prefer **close-up shots**, **people-centric actions**, and **natural environments**
  - Use keywords that are **b-roll friendly**, **cinematic**, and easy to match with voiceover timing
  - **Maintain visual consistency** - if chunk 1 shows "sales team", don't suddenly switch to "random office workers"

  ## 🔍 Enhanced Stock Keyword Best Practices
  
  **Formula**: [Product Context] + [Action] + [Setting/Emotion]
  
  Examples:
  - WhatsApp Sales Tool: "sales manager receiving WhatsApp notification office"
  - Fitness App: "person checking workout progress phone gym"  
  - Food Delivery: "hungry customer opening delivery app kitchen"
  - Investment Platform: "investor reviewing portfolio growth laptop"

  **Include context + action + emotion + setting**, but anchor with product relevance:
  - "WhatsApp business owner smiling at phone messages"
  - "sales team celebrating CRM conversion dashboard" 
  - "fitness enthusiast tracking progress on app"

  ## 🎬 Product Story Consistency
  
  Ensure your keywords tell a cohesive story about THIS specific product:
  - **Problem chunks**: Show industry-specific struggles (sales team missing WhatsApp leads)
  - **Solution chunks**: Show product-specific actions (AI organizing WhatsApp conversations)  
  - **Benefit chunks**: Show product-specific outcomes (sales dashboard showing conversions)
  - **CTA chunks**: Show product-specific success (business owner celebrating WhatsApp sales)

  ## 🧪 Output Format (JSON)
  \`\`\`json
  {
    "chunks": [
      {
        "script": "<chunk of the script>",
        "keyword": "<product-anchored stock search keyword>"
      },
      ...
    ]
  }
  \`\`\`

  ## 🚨 Final Check Before Output:
  
  For each keyword, ask:
  1. **"Does this keyword clearly connect to [PRODUCT NAME/TYPE]?"**
  2. **"Would someone know this video is about [INDUSTRY/SOLUTION] from this visual?"**  
  3. **"Is this keyword specific enough to maintain product context but generic enough to find on stock platforms?"**

  If any answer is NO, revise the keyword to include more product-specific context.

  ---

  ## CONTEXT FOR THIS PROJECT

  ### Product/Business Details:
  ${getBusinessDetailsString(params.business_details)}

  ### Script to Analyze:
  ${params.script}

  ---

  **Remember: Every keyword should pass the "product context test" - could this visual belong in an ad specifically about THIS product/service?**
`;
};

export const getVideoAdDirectorPromptV6 = (params: {
  business_details: Partial<IBannerbotBusinessDetails>;
  script: string;
}): string => {
  return `
  You are a creative advertising strategist and AI assistant helping generate **high-conversion keywords** for stock videos used in short video ads on platforms like **Meta, TikTok, and Instagram Reels**.

  ## 🎯 Goal
  You will be given:
  - A full ad **video script**
  - A brief **product description**

  Your task is to:
  1. **Break the script into exactly 4 meaningful chunks** (each representing a distinct visual moment or idea)
  2. For each chunk, generate **exactly one relevant stock video keyword or phrase** that:
    - Reflects the **core message** of the product, with focus on the chunk
    - Describes a **real-life action or scene** likely to be available on stock platforms
    - Is **optimized for short-form video ads** and high visual engagement

  ## 📱 Important Visual Guidelines
  - Prioritize **mobile-friendly, portrait-oriented scenes**
  - Prefer **close-up shots**, **people-centric actions**, and **natural environments**
  - Use keywords that are **b-roll friendly**, **cinematic**, and easy to match with voiceover timing

  ## 🔍 Stock Keyword Best Practices
  - Be **descriptive, not literal** (don’t use product/brand names)
  - Focus on **people doing relatable things** (not just objects)
  - Include **context + action + emotion + setting**, e.g.:
    - “woman smiling while scrolling phone on couch”
    - “man using skincare in morning light”
    - “family unboxing package with excitement”

  ## 🧪 Output Format (JSON)
  You must produce **exactly 4 chunks** and **exactly 4 keywords** in the following format:
  \`\`\`json
  {
    "chunks": [
      {
        "script": "<chunk 1 of the script>",
        "keyword": "<best stock search keyword or phrase>"
      },
      {
        "script": "<chunk 2 of the script>",
        "keyword": "<best stock search keyword or phrase>"
      },
      {
        "script": "<chunk 3 of the script>",
        "keyword": "<best stock search keyword or phrase>"
      },
      {
        "script": "<chunk 4 of the script>",
        "keyword": "<best stock search keyword or phrase>"
      }
    ]
  }
  \`\`\`

  ---

  ## CONTEXT FOR THIS PROJECT

  ### Product/Business Details:
  ${getBusinessDetailsString(params.business_details)}

  ### Script to Analyze:
  ${params.script}
`;
};

export const getVideoRelevancePrompt = (params: {
  keyword: string;
  business_details: Partial<IBannerbotBusinessDetails>;
  videos: Array<{ id: string; description: string }>;
}): string => {
  return `
  You are assisting in selecting the most relevant stock videos for a short-form video ad campaign.  
  These videos are sourced from Pexels using a specific search keyword.  
  Your task is to score how well each video matches **both** the keyword and the product being promoted, so the most suitable videos can be used in the ad.

  ## Scoring Rules
  - Score between **0 and 100** (100 = perfect match, 0 = no relevance)
  - Consider **keyword meaning** and **product context**
  - Be strict — only high scores for videos that are clearly relevant and usable in a short-form mobile ad
  - Output **only the JSON object** with video IDs as keys and their scores as values. No extra text, no explanations.

  ## Output Example
  \`\`\`json
  {
    "12345": 87,
    "67890": 45
  }
  \`\`\`

  ---

  ### Keyword:
  ${params.keyword}

  ### Product/Business Details:
  ${getBusinessDetailsString(params.business_details)}

  ### Videos to Evaluate:
  ${params.videos
    .map((v) => `ID: ${v.id} — Description: ${v.description}`)
    .join('\n')}
`;
};

export const getBaseVideoFromProductPrompt = (params: {
  business_details: Partial<IBannerbotBusinessDetails>;
  script: string;
}): string => {
  return `
  You are a video prompt generator for text-to-video AI models. Your task is to create detailed, actionable video prompts that will generate relevant base videos for product advertisements.

  Given:
  - Product Description:
    ${getBusinessDetailsString(params.business_details)}
  - Script: ${params.script}

  Generate a video prompt that creates a video showing:

  **Visual Elements to Include:**
  1. **Primary Scene**: Show the product being used in its natural environment
  2. **User Interaction**: Display people actively using/benefiting from the product
  3. **Problem-Solution Flow**: Subtly show the problem the product solves
  4. **Professional Quality**: Ensure the video looks polished and commercial-grade

  **Video Prompt Structure:**
  Create a prompt following this format:

  "professional commercial video showing [SPECIFIC USER TYPE] [SPECIFIC ACTION] with [PRODUCT TYPE]. [DETAILED VISUAL DESCRIPTION]. [CAMERA MOVEMENT]. [LIGHTING AND MOOD]. [ENDING VISUAL]. Style: clean, modern, professional commercial advertisement."

  **Guidelines:**
  - Be specific about who is using the product (job title, demographics if relevant)
  - Show clear before/after or problem/solution scenarios
  - Include appropriate settings/environments
  - Specify camera movements (smooth pan, close-up, wide shot, etc.)
  - Mention lighting (bright, professional, natural light, etc.)
  - Keep it realistic and achievable for AI video generation
  - Avoid complex transitions or effects that AI might struggle with
  - Focus on one main action/benefit per video segment

  **IMPORTANT:**
  - keep the prompt size less than 1000 characters

  **Output format**
  \`\`\`json
  {
    "prompt": "",
  }
  \`\`\`
  `;
};

export const getTwoSceneVideoPromptsFromProduct = (params: {
  business_details: Partial<IBannerbotBusinessDetails>;
  script: string;
}): string => {
  return `
  You are a video prompt generator for text-to-video AI models. Your task is to create 2 detailed, actionable video prompts that will generate relevant base videos for product advertisements. Each video will be 5 seconds long, creating a total of 10 seconds of content.

  Given:
  - Product Description:
    ${getBusinessDetailsString(params.business_details)}
  - Script: ${params.script}

  Generate 2 distinct video prompts that create a cohesive story:

  **Scene 1 (First 5 seconds)**: Should introduce the problem or set the context
  **Scene 2 (Second 5 seconds)**: Should show the solution or product benefit

  **Visual Elements to Include:**
  1. **Primary Scene**: Show the product being used in its natural environment
  2. **User Interaction**: Display people actively using/benefiting from the product
  3. **Problem-Solution Flow**: Scene 1 shows problem, Scene 2 shows solution
  4. **Professional Quality**: Ensure both videos look polished and commercial-grade

  **Video Prompt Structure for each scene:**
  "professional commercial video showing [SPECIFIC USER TYPE] [SPECIFIC ACTION] with [PRODUCT TYPE]. [DETAILED VISUAL DESCRIPTION]. [CAMERA MOVEMENT]. [LIGHTING AND MOOD]. [ENDING VISUAL]. Style: clean, modern, professional commercial advertisement."

  **Guidelines:**
  - Be specific about who is using the product (job title, demographics if relevant)
  - Scene 1: Focus on the problem or current situation
  - Scene 2: Focus on the solution and benefits
  - Include appropriate settings/environments
  - Specify camera movements (smooth pan, close-up, wide shot, etc.)
  - Mention lighting (bright, professional, natural light, etc.)
  - Keep it realistic and achievable for AI video generation
  - Avoid complex transitions or effects that AI might struggle with
  - Each scene should be self-contained but complementary

  **IMPORTANT:**
  - Keep each prompt size less than 1000 characters
  - Make sure the two scenes flow logically together

  **Output format**
  \`\`\`json
  {
    "scene1_prompt": "",
    "scene2_prompt": ""
  }
  \`\`\`
  `;
};
