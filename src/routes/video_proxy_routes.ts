import { Router, type Response } from 'express';
import axios from 'axios';
import logger from '../modules/logger';
import { type IApiRequest } from '../types';

const router = Router();

/**
 * Proxy endpoint for Pollo AI videos to add CORS headers
 * This solves the black video issue in Remotion by serving videos with proper CORS headers
 */
router.get('/pollo-video-proxy', async (req: IApiRequest, res: Response) => {
  try {
    const { url } = req.query as { url: string };
    
    if (!url) {
      return res.status(400).json({ error: 'Missing video URL parameter' });
    }

    // Validate that it's a Pollo AI video URL for security
    if (!url.includes('videocdn.pollo.ai')) {
      return res.status(400).json({ error: 'Invalid video URL - only Pollo AI videos are allowed' });
    }

    logger.info(`Proxying Pollo AI video: ${url}`);

    // Set CORS headers
    res.set({
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
      'Access-Control-Allow-Headers': 'Range, Content-Range, Content-Length, Content-Type',
      'Access-Control-Expose-Headers': 'Content-Range, Content-Length, Accept-Ranges',
    });

    // Handle preflight requests
    if (req.method === 'OPTIONS') {
      return res.status(200).end();
    }

    // Forward range headers for video streaming
    const headers: Record<string, string> = {};
    if (req.headers.range) {
      headers.Range = req.headers.range;
    }

    // Fetch the video from Pollo AI
    const response = await axios({
      method: 'GET',
      url,
      headers,
      responseType: 'stream',
    });

    // Forward response headers
    res.set({
      'Content-Type': response.headers['content-type'] || 'video/mp4',
      'Content-Length': response.headers['content-length'],
      'Accept-Ranges': 'bytes',
      'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
    });

    // Handle partial content responses
    if (response.status === 206) {
      res.status(206);
      res.set('Content-Range', response.headers['content-range']);
    }

    // Pipe the video stream to the response
    response.data.pipe(res);

    response.data.on('error', (error: Error) => {
      logger.error('Error streaming video:', error);
      if (!res.headersSent) {
        res.status(500).json({ error: 'Error streaming video' });
      }
    });

  } catch (error) {
    logger.error('Error in video proxy:', error);
    if (!res.headersSent) {
      res.status(500).json({ error: 'Internal server error' });
    }
  }
});

export default router;
