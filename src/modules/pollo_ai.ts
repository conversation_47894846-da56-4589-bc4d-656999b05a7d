import axios, { type AxiosError } from 'axios';
import fs from 'fs';
import path from 'path';
import logger from './logger';

const POLLO_AI_API_KEY = process.env.POLLO_AI_API_KEY ?? '';

export enum POLLO_AI_VIDEO_ROUTES {
  'SEEDANCE_LITE' = '/bytedance/seedance',
  'SEEDANCE_PRO' = '/bytedance/seedance-pro',
}

export enum POLLO_AI_VIDEO_RESOLUTION {
  '480p' = '480p',
  '720p' = '720p',
  '1080p' = '1080p',
}

export enum POLLO_AI_VIDEO_ASPECT_RATIO {
  RATIO_16_9 = '16:9',
  RATIO_9_16 = '9:16',
  RATIO_4_3 = '4:3',
  RATIO_3_4 = '3:4',
  RATIO_1_1 = '1:1',
}

export enum POLLO_AI_VIDEO_LENGTH {
  FIVE_SECONDS = 5,
  TEN_SECONDS = 10,
}

export const generateAiVideo = async (
  route: POLLO_AI_VIDEO_ROUTES,
  input: {
    prompt: string;
    resolution: POLLO_AI_VIDEO_RESOLUTION;
    length: POLLO_AI_VIDEO_LENGTH;
    aspectRatio?: POLLO_AI_VIDEO_ASPECT_RATIO; // Optional for text-to-video
    image?: string; // Optional for text-to-video
    seed?: number; // Optional
    cameraFixed?: boolean; // Optional
  },
  webhookUrl?: string, // Optional webhook URL
): Promise<{ taskId: string; status: string }> => {
  const BASE_URL = 'https://pollo.ai/api/platform/generation';
  try {
    const data: any = {
      input,
    };

    // Add webhook URL if provided
    if (webhookUrl) {
      data.webhookUrl = webhookUrl;
    }

    logger.info('pollo request body -> ', data);

    const response = await axios.post(BASE_URL + route, data, {
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': POLLO_AI_API_KEY,
      },
    });

    // Log the actual response structure to understand the API response format
    logger.info('pollo api response -> ', response.data);

    // Validate that we have the expected response structure
    if (!response.data) {
      throw new Error('Empty response from Pollo AI API');
    }

    // Check if the response has taskId directly or in a nested structure
    let taskId: string;
    let status: string;

    if (response.data.taskId) {
      // Direct taskId in response
      taskId = response.data.taskId;
      status = response.data.status || 'unknown';
    } else if (response.data.data?.taskId) {
      // taskId nested in data object
      taskId = response.data.data.taskId;
      status = response.data.data.status || 'unknown';
    } else if (response.data.id) {
      // Some APIs use 'id' instead of 'taskId'
      taskId = response.data.id;
      status = response.data.status || 'unknown';
    } else {
      // Log the full response structure for debugging
      logger.error(
        'Unexpected Pollo AI response structure:',
        JSON.stringify(response.data, null, 2),
      );
      throw new Error('taskId not found in Pollo AI response');
    }

    if (!taskId) {
      throw new Error('Invalid taskId received from Pollo AI API');
    }

    logger.info(`Successfully created Pollo AI task with ID: ${taskId}`);

    return { taskId, status };
  } catch (error) {
    logger.error(
      'Error in generateAiVideo:',
      (error as AxiosError).response?.data || error,
    );
    throw error;
  }
};

interface ITaskStatusResponse {
  taskId: string;
  generations: Array<{
    id: string;
    status: 'waiting' | 'succeed' | 'failed' | 'processing';
    failMsg?: string;
    url?: string;
    mediaType: 'image' | 'video' | 'audio' | 'text';
    createdDate: string;
    updatedDate: string;
  }>;
}

export const getTaskStatus = async (
  taskId: string,
): Promise<ITaskStatusResponse> => {
  try {
    if (!taskId) {
      throw new Error('taskId is required for getTaskStatus');
    }

    const url = `https://pollo.ai/api/platform/generation/${taskId}/status`;

    logger.info(`Checking task status for taskId: ${taskId}`);

    const response = await axios.get(url, {
      headers: { 'x-api-key': POLLO_AI_API_KEY },
    });

    logger.info(`Task status response for ${taskId}:`, response.data);

    // Handle the nested response structure
    if (response.data?.data) {
      // The actual task status data is nested in the 'data' field
      return response.data.data;
    } else if (response.data?.taskId) {
      // Direct response format
      return response.data;
    } else {
      logger.error(
        `Invalid task status response for ${taskId}:`,
        response.data,
      );
      throw new Error('Invalid task status response format');
    }
  } catch (error) {
    const axiosError = error as AxiosError;
    logger.error(
      `Error getting task status for taskId ${taskId}:`,
      axiosError.response?.data || error,
    );

    if (axiosError.response?.status === 404) {
      throw new Error(
        `Task not found: ${taskId}. This may indicate the task was not created successfully.`,
      );
    }

    throw error;
  }
};

/**
 * Downloads a video from a URL and saves it locally for debugging
 */
export const downloadVideoLocally = async (
  videoUrl: string,
  fileName: string,
  localDir: string = '/tmp/pollo-ai-videos',
): Promise<string> => {
  try {
    // Create directory if it doesn't exist
    if (!fs.existsSync(localDir)) {
      fs.mkdirSync(localDir, { recursive: true });
    }

    const localFilePath = path.join(localDir, fileName);

    logger.info(`Downloading video from ${videoUrl} to ${localFilePath}`);

    // Download the video
    const response = await axios({
      method: 'GET',
      url: videoUrl,
      responseType: 'stream',
    });

    // Create write stream
    const writer = fs.createWriteStream(localFilePath);

    // Pipe the response to the file
    response.data.pipe(writer);

    // Wait for download to complete
    await new Promise<void>((resolve, reject) => {
      writer.on('finish', () => {
        resolve();
      });
      writer.on('error', reject);
    });

    logger.info(`Video successfully downloaded to: ${localFilePath}`);

    // Get file stats for verification
    const stats = fs.statSync(localFilePath);
    logger.info(
      `Downloaded video size: ${(stats.size / 1024 / 1024).toFixed(2)} MB`,
    );

    return localFilePath;
  } catch (error) {
    logger.error(`Error downloading video from ${videoUrl}:`, error);
    throw error;
  }
};

/**
 * Checks if a video URL is accessible and logs its properties
 */
export const checkVideoAccessibility = async (
  videoUrl: string,
  sceneName: string = 'unknown',
): Promise<boolean> => {
  try {
    logger.info(`Checking accessibility for ${sceneName} video: ${videoUrl}`);

    // Make a HEAD request to check if the video is accessible
    const response = await axios.head(videoUrl);

    logger.info(`${sceneName} video accessibility check:`, {
      status: response.status,
      contentType: response.headers['content-type'],
      contentLength: response.headers['content-length'],
      lastModified: response.headers['last-modified'],
      cacheControl: response.headers['cache-control'],
    });

    // Check if it's actually a video
    const contentType = response.headers['content-type'];
    if (!contentType?.startsWith('video/')) {
      logger.error(
        `${sceneName} URL does not point to a video file. Content-Type: ${contentType}`,
      );
      return false;
    }

    // Check content length
    const contentLength = response.headers['content-length'];
    if (contentLength?.toString()) {
      const sizeInMB = parseInt(contentLength) / 1024 / 1024;
      logger.info(`${sceneName} video size: ${sizeInMB.toFixed(2)} MB`);

      if (sizeInMB < 0.1) {
        logger.warn(
          `${sceneName} video seems very small (${sizeInMB.toFixed(2)} MB), might be corrupted`,
        );
      }
    }

    return true;
  } catch (error) {
    logger.error(`Error checking accessibility for ${sceneName} video:`, error);
    return false;
  }
};

/**
 * Tests video playability and format compatibility
 */
export const testVideoCompatibility = async (
  videoUrl: string,
  sceneName: string = 'unknown',
): Promise<{
  isAccessible: boolean;
  contentType: string;
  size: number;
  headers: Record<string, string>;
}> => {
  try {
    logger.info(`Testing video compatibility for ${sceneName}: ${videoUrl}`);

    // Test with both HEAD and partial GET request
    const headResponse = await axios.head(videoUrl);

    // Test partial content support (important for video streaming)
    const partialResponse = await axios.get(videoUrl, {
      headers: {
        Range: 'bytes=0-1023', // First 1KB
      },
      responseType: 'arraybuffer',
    });

    const contentType = headResponse.headers['content-type'] || '';
    const contentLength = parseInt(
      headResponse.headers['content-length'] || '0',
    );
    const acceptsRanges = headResponse.headers['accept-ranges'] || '';
    const cacheControl = headResponse.headers['cache-control'] || '';

    logger.info(`${sceneName} video compatibility test results:`, {
      contentType,
      size: `${(contentLength / 1024 / 1024).toFixed(2)} MB`,
      acceptsRanges,
      cacheControl,
      partialContentSupported: partialResponse.status === 206,
      corsHeaders: {
        'access-control-allow-origin':
          headResponse.headers['access-control-allow-origin'],
        'access-control-allow-methods':
          headResponse.headers['access-control-allow-methods'],
        'access-control-allow-headers':
          headResponse.headers['access-control-allow-headers'],
      },
    });

    // Check if it's a valid video format for web
    const validVideoTypes = ['video/mp4', 'video/webm', 'video/ogg'];
    const isValidFormat = validVideoTypes.some((type) =>
      contentType.includes(type),
    );

    if (!isValidFormat) {
      logger.warn(
        `${sceneName} video format may not be web-compatible: ${contentType}`,
      );
    }

    // Check if video supports range requests (important for streaming)
    if (acceptsRanges !== 'bytes') {
      logger.warn(
        `${sceneName} video does not support range requests, may cause streaming issues`,
      );
    }

    return {
      isAccessible: true,
      contentType,
      size: contentLength,
      headers: headResponse.headers as Record<string, string>,
    };
  } catch (error) {
    logger.error(`Error testing video compatibility for ${sceneName}:`, error);
    return {
      isAccessible: false,
      contentType: '',
      size: 0,
      headers: {},
    };
  }
};
